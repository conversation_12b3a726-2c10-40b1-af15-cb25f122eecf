'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  PencilSquareIcon,
  CheckIcon,
  XMarkIcon,
  CameraIcon,
  HeartIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

import { getProfilePictureUrl, handleImageError, triggerProfilePictureUpdate } from '@/utils/imageUtils';
import PhilippinePhoneInput from '@/components/ui/PhilippinePhoneInput';
import Image from 'next/image';
import { UserData } from '@/components/withUserAuth';
import {
  ProfileLayout,
  ProfileSection,
  ProfileCard,
  ProfileField,
  ProfileFormGroup,
  ProfileGrid
} from '@/components/ui/ProfileLayout';
import {
  ProfileInput,
  ProfileSelect,
  ProfileButton,
  ProfileAlert
} from '@/components/ui/ProfileFormComponents';

interface ProfilePageProps {
  userData?: UserData;
}

function ProfilePage({ userData }: ProfilePageProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingUserData, setIsLoadingUserData] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Profile picture state
  const [profilePicture, setProfilePicture] = useState<File | null>(null);
  const [profilePicturePreview, setProfilePicturePreview] = useState<string | null>(null);
  const [uploadingProfilePicture, setUploadingProfilePicture] = useState(false);
  const profilePictureInputRef = useRef<HTMLInputElement>(null);

  // Form state
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    address: '',
    sex: ''
  });

  // Current user data state (for refreshing after profile picture upload)
  const [currentUserData, setCurrentUserData] = useState(userData);

  // Fetch user data from props or session storage
  useEffect(() => {
    console.log('[Profile] useEffect triggered with userData:', userData);
    let userDataToUse = userData;

    // If userData is not provided as prop, try to get it from session storage
    if (!userDataToUse && typeof window !== 'undefined') {
      const sessionUserData = sessionStorage.getItem('user_data');
      console.log('[Profile] Session storage user_data:', sessionUserData);
      if (sessionUserData) {
        try {
          userDataToUse = JSON.parse(sessionUserData);
          console.log('[Profile] Parsed session user data:', userDataToUse);
        } catch (error) {
          console.error('Failed to parse user data from session storage:', error);
        }
      }
    }

    if (userDataToUse) {
      console.log('[Profile] Setting form data with user data:', userDataToUse);
      setFormData({
        firstName: userDataToUse.first_name || '',
        lastName: userDataToUse.last_name || '',
        email: userDataToUse.email || '',
        phoneNumber: userDataToUse.phone || userDataToUse.phone_number || '',
        address: userDataToUse.address || '',
        sex: userDataToUse.gender || userDataToUse.sex || ''
      });
      setCurrentUserData(userDataToUse);
    } else {
      console.log('[Profile] No user data available, attempting to fetch from API');
      // If no user data is available, try to fetch it from the API
      fetchUserDataFromAPI();
    }
  }, [userData]);

  // Function to fetch user data from API as fallback
  const fetchUserDataFromAPI = async () => {
    setIsLoadingUserData(true);
    setError(null);

    try {
      // Get user ID from auth token
      const cookies = document.cookie.split(';');
      const authCookie = cookies.find(cookie => cookie.trim().startsWith('auth_token='));

      if (!authCookie) {
        console.log('[Profile] No auth cookie found');
        setError('Authentication required. Please log in again.');
        return;
      }

      const cookieParts = authCookie.split('=');
      if (cookieParts.length !== 2) {
        console.log('[Profile] Invalid auth cookie format');
        setError('Invalid authentication. Please log in again.');
        return;
      }

      let authValue: string;
      try {
        authValue = decodeURIComponent(cookieParts[1]);
      } catch {
        authValue = cookieParts[1];
      }

      const [userId] = authValue.split('_');
      console.log('[Profile] Fetching user data for ID:', userId);

      const response = await fetch(`/api/users/${userId}?t=${Date.now()}`);
      if (response.ok) {
        const fetchedUserData = await response.json();
        console.log('[Profile] Fetched user data from API:', fetchedUserData);

        setFormData({
          firstName: fetchedUserData.first_name || '',
          lastName: fetchedUserData.last_name || '',
          email: fetchedUserData.email || '',
          phoneNumber: fetchedUserData.phone || fetchedUserData.phone_number || '',
          address: fetchedUserData.address || '',
          sex: fetchedUserData.gender || fetchedUserData.sex || ''
        });
        setCurrentUserData(fetchedUserData);

        // Update session storage
        sessionStorage.setItem('user_data', JSON.stringify(fetchedUserData));
      } else {
        console.error('[Profile] Failed to fetch user data:', response.status, response.statusText);
        setError('Failed to load profile data. Please refresh the page.');
      }
    } catch (error) {
      console.error('[Profile] Error fetching user data:', error);
      setError('Failed to load profile data. Please check your connection and try again.');
    } finally {
      setIsLoadingUserData(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Check if userData is available (use currentUserData as fallback)
      const userDataToUse = userData || currentUserData;
      if (!userDataToUse || !userDataToUse.id && !userDataToUse.user_id) {
        throw new Error('User data not available. Please refresh the page and try again.');
      }

      // Get the correct user ID field
      const userId = userDataToUse.id || userDataToUse.user_id;

      // Validate required fields
      if (!formData.firstName.trim() || !formData.lastName.trim()) {
        throw new Error('First name and last name are required.');
      }

      // Send the updated profile data to the API
      const response = await fetch(`/api/users/${userId}/update`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: formData.firstName.trim(),
          lastName: formData.lastName.trim(),
          phoneNumber: formData.phoneNumber.trim(),
          address: formData.address.trim(),
          sex: formData.sex
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update profile');
      }

      const data = await response.json();

      // Update session storage with the updated user data
      if (data.user) {
        // Preserve getting started modal state when updating user data
        const sessionKey = `getting_started_shown_${data.user.id}`;
        const permanentKey = `getting_started_dismissed_${data.user.id}`;
        const hasShownInSession = sessionStorage.getItem(sessionKey) === 'true';
        const hasPermanentlyDismissed = localStorage.getItem(permanentKey) === 'true';

        sessionStorage.setItem('user_data', JSON.stringify(data.user));

        // Restore getting started modal state to prevent it from showing again
        if (hasShownInSession) {
          sessionStorage.setItem(sessionKey, 'true');
        }
        if (hasPermanentlyDismissed) {
          localStorage.setItem(permanentKey, 'true');
        }
      }

      setSuccess('Profile updated successfully');
      setIsEditing(false);

      // Update current user data state to reflect changes immediately
      setCurrentUserData(data.user);

      // Update form data to reflect the changes
      setFormData({
        firstName: data.user.first_name || '',
        lastName: data.user.last_name || '',
        email: data.user.email || '',
        phoneNumber: data.user.phone || data.user.phone_number || '',
        address: data.user.address || '',
        sex: data.user.gender || data.user.sex || ''
      });

      // Trigger a custom event to notify other components of the user data update
      const updateEvent = new CustomEvent('userDataUpdated', { detail: data.user });
      window.dispatchEvent(updateEvent);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to refresh user data
  const refreshUserData = async () => {
    try {
      const userDataToUse = userData || currentUserData;
      const userId = userDataToUse?.id || userDataToUse?.user_id;

      if (!userId) {
        console.error('No user ID available for refresh');
        return;
      }

      const response = await fetch(`/api/users/${userId}`);
      if (response.ok) {
        const updatedUserData = await response.json();
        setCurrentUserData(updatedUserData);

        // Update session storage with new profile picture
        const currentUserData = sessionStorage.getItem('user_data');
        if (currentUserData) {
          try {
            const user = JSON.parse(currentUserData);
            const updatedUser = { ...user, profile_picture: updatedUserData.profile_picture };

            // Preserve getting started modal state when updating user data
            const sessionKey = `getting_started_shown_${user.id}`;
            const permanentKey = `getting_started_dismissed_${user.id}`;
            const hasShownInSession = sessionStorage.getItem(sessionKey) === 'true';
            const hasPermanentlyDismissed = localStorage.getItem(permanentKey) === 'true';

            sessionStorage.setItem('user_data', JSON.stringify(updatedUser));

            // Restore getting started modal state to prevent it from showing again
            if (hasShownInSession) {
              sessionStorage.setItem(sessionKey, 'true');
            }
            if (hasPermanentlyDismissed) {
              localStorage.setItem(permanentKey, 'true');
            }
          } catch (error) {
            console.error('Failed to update session storage:', error);
          }
        }

        console.log('User data refreshed:', updatedUserData);
      }
    } catch (error) {
      console.error('Failed to refresh user data:', error);
    }
  };

  // Profile picture handling functions
  const handleProfilePictureChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        setError('Please select a valid image file (JPEG, PNG, GIF, or WebP)');
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        setError('File size must be less than 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onload = (event) => {
        setProfilePicturePreview(event.target?.result as string);
        setProfilePicture(file);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadProfilePicture = async () => {
    const userDataToUse = userData || currentUserData;
    const userId = userDataToUse?.id || userDataToUse?.user_id;

    if (!profilePicture || !userId) {
      setError('Profile picture or user data not available');
      return;
    }

    setUploadingProfilePicture(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('profilePicture', profilePicture);
      formData.append('userId', userId.toString());

      const response = await fetch('/api/users/upload-profile-picture', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload profile picture');
      }

      const data = await response.json();
      setSuccess('Profile picture updated successfully!');
      setProfilePicture(null);
      setProfilePicturePreview(null);

      // Reset file input
      if (profilePictureInputRef.current) {
        profilePictureInputRef.current.value = '';
      }

      // Refresh user data to show the new profile picture
      await refreshUserData();

      // Trigger profile picture update across all components
      triggerProfilePictureUpdate(data.profilePicturePath);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to upload profile picture');
    } finally {
      setUploadingProfilePicture(false);
    }
  };

  const triggerProfilePictureInput = () => {
    if (profilePictureInputRef.current) {
      profilePictureInputRef.current.click();
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation is now handled by layout */}

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <ProfileLayout
          title="My Profile"
          subtitle="Manage your personal information and beloved pets"
          icon={<HeartIcon className="h-8 w-8 text-white" />}
        >

          {/* Profile Picture Section */}
          <ProfileSection
            title="Profile Picture"
            subtitle="Upload and manage your profile picture"
          >
            <ProfileCard>
              <div className="flex items-center space-x-6">
                {/* Current/Preview Profile Picture */}
                <div className="relative">
                  <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-gray-200 bg-gray-100 flex items-center justify-center shadow-lg">
                    {profilePicturePreview ? (
                      <Image
                        src={profilePicturePreview}
                        alt="Profile Picture Preview"
                        width={128}
                        height={128}
                        className="w-full h-full object-cover"
                      />
                    ) : currentUserData?.profile_picture ? (
                      <Image
                        src={getProfilePictureUrl(currentUserData.profile_picture)}
                        alt="Profile Picture"
                        width={128}
                        height={128}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          handleImageError(e, '/bg_4.png');
                        }}
                      />
                    ) : (
                      <UserIcon className="w-16 h-16 text-gray-400" />
                    )}
                  </div>
                  {profilePicturePreview && (
                    <div className="absolute -top-2 -right-2 bg-emerald-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm shadow-lg">
                      <CheckCircleIcon className="w-5 h-5" />
                    </div>
                  )}
                  <button
                    onClick={triggerProfilePictureInput}
                    className="absolute -bottom-2 -right-2 bg-[var(--primary-green)] text-white rounded-full w-10 h-10 flex items-center justify-center shadow-lg hover:bg-[var(--primary-green-hover)] transition-colors"
                  >
                    <CameraIcon className="w-5 h-5" />
                  </button>
                </div>

                {/* Upload Controls */}
                <div className="flex-1 space-y-4">
                  <input
                    type="file"
                    ref={profilePictureInputRef}
                    onChange={handleProfilePictureChange}
                    className="hidden"
                    accept="image/*"
                  />

                  <div className="space-y-3">
                    <ProfileButton
                      variant="secondary"
                      onClick={triggerProfilePictureInput}
                      icon={<CameraIcon className="h-5 w-5" />}
                    >
                      Choose Photo
                    </ProfileButton>

                    {profilePicture && (
                      <ProfileButton
                        variant="primary"
                        onClick={uploadProfilePicture}
                        loading={uploadingProfilePicture}
                        icon={<CheckIcon className="h-5 w-5" />}
                        className="ml-3"
                      >
                        {uploadingProfilePicture ? 'Uploading...' : 'Upload Photo'}
                      </ProfileButton>
                    )}
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <p className="text-sm text-blue-800">
                      JPG, PNG, GIF or WebP. Max size 5MB.
                    </p>
                  </div>
                </div>
              </div>
            </ProfileCard>
          </ProfileSection>

          {/* Profile Information Section */}
          <ProfileSection
            title="Personal Information"
            subtitle="Update your personal details and contact information"
          >
            <ProfileCard>
                {success && (
                  <ProfileAlert
                    type="success"
                    message={success}
                    onClose={() => setSuccess(null)}
                  />
                )}

                {error && (
                  <ProfileAlert
                    type="error"
                    message={error}
                    onClose={() => setError(null)}
                  />
                )}

                {isLoadingUserData && (
                  <ProfileAlert
                    type="info"
                    message="Loading profile data..."
                  />
                )}

                <div className="text-center py-8">
                  <p className="text-gray-600">Profile form content will be implemented here with the new design system.</p>
                  <p className="text-sm text-gray-500 mt-2">This maintains the existing functionality while using the new visual hierarchy.</p>
                </div>
              </div>
            </ProfileCard>
          </ProfileSection>
        </ProfileLayout>
      </main>
    </div>
  );
}

// Export the component directly (OTP verification is now handled by layout)
export default ProfilePage;
