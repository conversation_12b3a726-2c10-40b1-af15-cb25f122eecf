'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { isAuthenticated, getUserId, getAccountType } from '@/utils/auth';
import { useToast } from './ToastContext';

export interface Notification {
  id: number;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  is_read: number;
  link: string | null;
  created_at: string;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  fetchNotifications: (unreadOnly?: boolean) => Promise<void>;
  markAsRead: (notificationId: number) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  removeNotification: (notificationId: number) => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { showToast } = useToast();

  // Track multiple timeouts for proper cleanup - fixes race condition
  const timeoutIdsRef = useRef<Set<NodeJS.Timeout>>(new Set());

  const fetchNotifications = async (unreadOnly = false) => {
    // Declare timeout ID at function scope to ensure cleanup works properly
    let timeoutId: NodeJS.Timeout | null = null;

    // Check if user is authenticated
    if (typeof window !== 'undefined' && !isAuthenticated()) {
      setNotifications([]);
      setUnreadCount(0);
      return { notifications: [], unreadCount: 0 };
    }

    try {
      setLoading(true);
      setError(null);

      // Determine user type
      const userId = getUserId();
      const userType = getAccountType();
      const isAdmin = userType === 'admin';
      const isBusiness = userType === 'business';

      // Simplified user ID validation - only check for null/undefined since getUserId() returns string | null
      // The API relies on authentication headers for user identity, so we don't need strict ID validation
      if (!userId) {
        // Don't throw an error, just set empty data and return gracefully
        // This makes the application more resilient to authentication edge cases
        setNotifications([]);
        setUnreadCount(0);
        return { notifications: [], unreadCount: 0 };
      }

      // Use the appropriate API endpoint based on user type
      let apiUrl = '';
      if (isAdmin) {
        apiUrl = '/api/admin/notifications';
      } else if (isBusiness) {
        apiUrl = `/api/cremation/notifications`;
      } else {
        apiUrl = '/api/user/notifications';
      }

      // Add query parameters properly
      const params = new URLSearchParams();
      if (unreadOnly) {
        params.append('unread_only', 'true');
      }
      
      // Add limit parameter for non-admin endpoints to prevent unlimited results
      if (!isAdmin) {
        params.append('limit', '50');
      }
      
      // Add cache-busting parameter to prevent stale data
      params.append('t', Date.now().toString());
      
      // Construct the final URL with proper query string
      apiUrl += `?${params.toString()}`;

      // Use timeout to prevent hanging requests
      const controller = new AbortController();
      timeoutId = setTimeout(() => controller.abort(), 8000);
      
      // Track timeout for cleanup - add to set to handle concurrent calls
      timeoutIdsRef.current.add(timeoutId);

      const response = await fetch(apiUrl, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        },
        signal: controller.signal
      });

      // Clear timeout since request completed and remove from tracking set
      if (timeoutId) {
        clearTimeout(timeoutId);
        const currentTimeoutIds = timeoutIdsRef.current;
        currentTimeoutIds.delete(timeoutId);
      }

      if (!response.ok) {
        // If unauthorized or any other error, just set empty data without throwing an error
        // This makes the application more resilient to authentication issues

        // For database connection errors, show a fallback message
        if (response.status === 500) {
          // Check if it's a database connection error
          try {
            const errorData = await response.json();
            if (errorData.details && errorData.details.includes('Too many connections')) {
              console.warn('Database connection limit reached. Notifications temporarily unavailable.');
            }
          } catch {
            // Ignore JSON parsing errors
          }
        }

        setNotifications([]);
        setUnreadCount(0);
        return { notifications: [], unreadCount: 0 };
      }

      // Check the content type to ensure it's JSON
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const _text = await response.text();
        setNotifications([]);
        setUnreadCount(0);
        return { notifications: [], unreadCount: 0 };
      }

      try {
        const data = await response.json();

        // Ensure data has the expected structure
        if (!data || typeof data !== 'object') {
          setNotifications([]);
          setUnreadCount(0);
          return { notifications: [], unreadCount: 0 };
        }

        // Set notifications with fallback to empty array if missing
        if (Array.isArray(data.notifications)) {
          const mappedNotifications = data.notifications.map((notif: any) => {
            // Convert is_read to ensure consistency (it might be TINYINT(1), BOOLEAN, or 0/1)
            const isRead = typeof notif.is_read === 'boolean' 
              ? (notif.is_read ? 1 : 0)
              : (notif.is_read ? 1 : 0);
              
            return {
              ...notif,
              // Ensure we have the correct structure for all notification types
              id: notif.id,
              title: notif.title || 'Notification',
              message: notif.message || '',
              type: notif.type || 'info',
              is_read: isRead,
              link: notif.link || null,
              created_at: notif.created_at || new Date().toISOString()
            };
          });
          
          setNotifications(mappedNotifications);
          
          // Calculate unread count consistently
          const unreadCount = data.notifications.filter((n: any) => {
            return n.is_read === false || n.is_read === 0;
          }).length;
          
          setUnreadCount(unreadCount);
        } else {
          // Fallback for any other structure
          setNotifications([]);
          setUnreadCount(0);
        }

        return data;
      } catch (parseError) {
        console.error('NotificationContext: Error parsing JSON:', parseError);
        setNotifications([]);
        setUnreadCount(0);
        return { notifications: [], unreadCount: 0 };
      }
    } catch (error) {
      console.error('NotificationContext: Fetch error:', error);
      
      // Clear timeout and remove from tracking set to prevent memory leaks
      if (timeoutId) {
        clearTimeout(timeoutId);
        const currentTimeoutIds = timeoutIdsRef.current;
        currentTimeoutIds.delete(timeoutId);
      }
      
      // Return empty data gracefully
      setNotifications([]);
      setUnreadCount(0);
      return { notifications: [], unreadCount: 0 };
    } finally {
      setLoading(false);
    }
  };

  // Mark a notification as read
  const markAsRead = async (notificationId: number) => {
    // Check if user is authenticated
    if (typeof window !== 'undefined' && !isAuthenticated()) {
      return;
    }

    try {
      // Use a generic endpoint and let the server determine the user type
      const response = await fetch('/api/user/notifications', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ notificationId: notificationId }),
      });

      if (!response.ok) {
        // If any error occurs, just log it and continue without throwing
        if (process.env.NODE_ENV === 'development') {
          console.warn('Failed to mark notification as read:', response.status);
        }
        return;
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, is_read: 1 }
            : notification
        )
      );

      setUnreadCount(prev => Math.max(0, prev - 1));

      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      showToast(errorMessage, 'error');
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    // Check if user is authenticated
    if (typeof window !== 'undefined' && !isAuthenticated()) {
      return;
    }

    try {
      // Use a generic endpoint and let the server determine the user type
      const response = await fetch('/api/user/notifications', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ markAll: true }),
      });

      if (!response.ok) {
        // If any error occurs, just log it and continue without throwing
        if (process.env.NODE_ENV === 'development') {
          console.warn('Failed to mark all notifications as read:', response.status);
        }
        return;
      }

      // Update local state
      setNotifications(prev =>
        prev.map(notification => ({ ...notification, is_read: 1 }))
      );

      setUnreadCount(0);

      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      showToast(errorMessage, 'error');
    }
  };

  // Remove a specific notification
  const removeNotification = async (notificationId: number) => {
    // Check if user is authenticated
    if (typeof window !== 'undefined' && !isAuthenticated()) {
      return;
    }

    // Validate notification ID
    if (!notificationId || isNaN(notificationId) || notificationId <= 0) {
      showToast('Invalid notification ID', 'error');
      return;
    }

    try {
      // Use a generic endpoint and let the server determine the user type
      const response = await fetch(`/api/user/notifications/${notificationId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove notification');
      }

      // Update local state - remove the notification from the list
      setNotifications(prev => prev.filter(notification => notification.id !== notificationId));

      // Update unread count if the removed notification was unread
      setUnreadCount(prev => {
        const removedNotification = notifications.find(n => n.id === notificationId);
        if (removedNotification && removedNotification.is_read === 0) {
          return Math.max(0, prev - 1);
        }
        return prev;
      });

      showToast('Notification removed successfully', 'success');
      return await response.json();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      showToast(errorMessage, 'error');
    }
  };

  // Initial fetch of notifications only if user is authenticated
  useEffect(() => {
    // Prevent multiple interval instances
    let intervalId: NodeJS.Timeout | null = null;
    // Capture ref value at the beginning of the effect to avoid ref warnings
    const timeoutIds = timeoutIdsRef.current;

    // Check if user is authenticated before fetching notifications
    if (typeof window !== 'undefined' && isAuthenticated()) {
      // Initial fetch with error handling
      fetchNotifications().catch(_err => {
        // Don't show error toast for initial load to avoid annoying users
      });

      // Set up polling to check for new notifications every 2 minutes (reduced from 1 minute)
      // This helps reduce server load and excessive API calls
      intervalId = setInterval(() => {
        // Check authentication again before each fetch
        if (isAuthenticated()) {
          // Wrap in try/catch to prevent unhandled promise rejections
          try {
            fetchNotifications().catch(_err => {
              // Silent fail for background updates
            });
          } catch {
          }
        } else {
          // If no longer authenticated, clear the interval
          if (intervalId) {
            clearInterval(intervalId);
            intervalId = null;
          }
        }
      }, 120000); // 2 minutes
    }

    // Clean up interval and any pending timeouts on component unmount
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
      }
      
      // Clear any pending timeouts from fetchNotifications
      // Use the captured ref value to avoid ref value change warning
      timeoutIds.forEach(timeoutId => {
        clearTimeout(timeoutId);
      });
      timeoutIds.clear();
    };
  }, []); // Empty dependency array to run only once on mount

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        loading,
        error,
        fetchNotifications,
        markAsRead,
        markAllAsRead,
        removeNotification,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
}
